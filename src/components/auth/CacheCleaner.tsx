import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'

export const CacheCleaner: React.FC = () => {
  const { user, profile, session, clearAllCache, forceSignOut } = useAuth()
  const [isVisible, setIsVisible] = React.useState(false)

  // Show cache cleaner if there are authentication inconsistencies
  React.useEffect(() => {
    // Show if user exists but no profile (profile fetch failed)
    // Or if session exists but user doesn't (stale session)
    const hasAuthIssues = (user && !profile) || (session && !user) || (!session && user)
    setIsVisible(hasAuthIssues)
  }, [user, profile, session])

  // Also show for 30 seconds on mount to help with immediate issues
  React.useEffect(() => {
    setIsVisible(true)
    const timer = setTimeout(() => {
      setIsVisible(false)
    }, 30000) // Hide after 30 seconds if no auth issues

    return () => clearTimeout(timer)
  }, [])

  if (!isVisible) return null

  return (
    <div className="fixed top-4 right-4 bg-red-50 border border-red-200 p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-bold text-red-800">Authentication Issues?</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-red-400 hover:text-red-600 text-lg leading-none"
        >
          ×
        </button>
      </div>
      <p className="text-sm text-red-600 mb-3">
        If you're seeing old user data or login issues, try clearing the cache.
      </p>
      <div className="flex gap-2">
        <Button
          onClick={clearAllCache}
          variant="destructive"
          size="sm"
        >
          Clear Cache
        </Button>
        <Button
          onClick={forceSignOut}
          variant="outline"
          size="sm"
          className="border-red-300 text-red-700 hover:bg-red-50"
        >
          Force Sign Out
        </Button>
      </div>
    </div>
  )
}
